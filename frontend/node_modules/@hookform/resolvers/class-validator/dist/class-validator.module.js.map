{"version": 3, "file": "class-validator.module.js", "sources": ["../src/class-validator.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { plainToClass } from 'class-transformer';\nimport { ValidationError, validate, validateSync } from 'class-validator';\nimport { FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrors = (\n  errors: ValidationError[],\n  validateAllFieldCriteria: boolean,\n  parsedErrors: FieldErrors = {},\n  path = '',\n) => {\n  return errors.reduce((acc, error) => {\n    const _path = path ? `${path}.${error.property}` : error.property;\n\n    if (error.constraints) {\n      const key = Object.keys(error.constraints)[0];\n      acc[_path] = {\n        type: key,\n        message: error.constraints[key],\n      };\n\n      const _e = acc[_path];\n      if (validateAllFieldCriteria && _e) {\n        Object.assign(_e, { types: error.constraints });\n      }\n    }\n\n    if (error.children && error.children.length) {\n      parseErrors(error.children, validateAllFieldCriteria, acc, _path);\n    }\n\n    return acc;\n  }, parsedErrors);\n};\n\nexport const classValidatorResolver: Resolver =\n  (schema, schemaOptions = {}, resolverOptions = {}) =>\n  async (values, _, options) => {\n    const { transformer, validator } = schemaOptions;\n    const data = plainToClass(schema, values, transformer);\n\n    const rawErrors = await (resolverOptions.mode === 'sync'\n      ? validateSync\n      : validate)(data, validator);\n\n    if (rawErrors.length) {\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrors(\n            rawErrors,\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.rawValues ? values : data,\n      errors: {},\n    };\n  };\n"], "names": ["parseErrors", "errors", "validateAllFieldCriteria", "parsedErrors", "path", "reduce", "acc", "error", "_path", "property", "constraints", "key", "Object", "keys", "type", "message", "_e", "assign", "types", "children", "length", "classValidatorResolver", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "validator", "data", "plainToClass", "transformer", "Promise", "resolve", "mode", "validateSync", "validate", "then", "rawErrors", "toNestErrors", "shouldUseNativeValidation", "criteriaMode", "validateFieldsNatively", "rawValues", "e", "reject"], "mappings": "6LAMA,IAAMA,EAAc,SAAdA,EACJC,EACAC,EACAC,EACAC,GAEA,gBAHAD,IAAAA,EAA4B,CAAE,QAC1B,IAAJC,IAAAA,EAAO,IAEAH,EAAOI,OAAO,SAACC,EAAKC,GACzB,IAAMC,EAAQJ,EAAUA,EAAQG,IAAAA,EAAME,SAAaF,EAAME,SAEzD,GAAIF,EAAMG,YAAa,CACrB,IAAMC,EAAMC,OAAOC,KAAKN,EAAMG,aAAa,GAC3CJ,EAAIE,GAAS,CACXM,KAAMH,EACNI,QAASR,EAAMG,YAAYC,IAG7B,IAAMK,EAAKV,EAAIE,GACXN,GAA4Bc,GAC9BJ,OAAOK,OAAOD,EAAI,CAAEE,MAAOX,EAAMG,aAErC,CAMA,OAJIH,EAAMY,UAAYZ,EAAMY,SAASC,QACnCpB,EAAYO,EAAMY,SAAUjB,EAA0BI,EAAKE,GAGtDF,CACT,EAAGH,EACL,EAEakB,EACX,SAACC,EAAQC,EAAoBC,GAAoB,YAAxCD,IAAAA,IAAAA,EAAgB,SAAmB,IAAfC,IAAAA,EAAkB,IAAE,SAC1CC,EAAQC,EAAGC,GAAO,IACvB,IAAqBC,EAAcL,EAAdK,UACfC,EAAOC,EAAaR,EAAQG,EADCF,EAA3BQ,aAC+C,OAAAC,QAAAC,SAEL,SAAzBT,EAAgBU,KACrCC,EACAC,GAAUP,EAAMD,IAAUS,KAFxBC,SAAAA,GAIN,OAAIA,EAAUlB,OACL,CACLK,OAAQ,GACRxB,OAAQsC,EACNvC,EACEsC,GACCX,EAAQa,2BACkB,QAAzBb,EAAQc,cAEZd,KAKNA,EAAQa,2BAA6BE,EAAuB,CAAA,EAAIf,GAEzD,CACLF,OAAQD,EAAgBmB,UAAYlB,EAASI,EAC7C5B,OAAQ,CAAA,GACR,EACJ,CAAC,MAAA2C,GAAA,OAAAZ,QAAAa,OAAAD,EAAA,CAAA,CAAA"}