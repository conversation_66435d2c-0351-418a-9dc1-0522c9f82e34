# AI Agent Platform - Environment Variables Template
# Copy this file to .env and fill in your actual values

# ==============================================
# ENVIRONMENT CONFIGURATION
# ==============================================
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-super-secret-key-change-this-in-production-minimum-32-characters
LOG_LEVEL=info

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
# PostgreSQL
POSTGRES_DB=ai_agent_platform
POSTGRES_USER=ai_agent_user
POSTGRES_PASSWORD=ai_agent_password
DATABASE_URL=**********************************************************/ai_agent_platform

# Redis
REDIS_PASSWORD=ai_agent_redis_password
REDIS_URL=redis://:ai_agent_redis_password@redis:6379/0

# ==============================================
# MESSAGE QUEUE CONFIGURATION
# ==============================================
KAFKA_BOOTSTRAP_SERVERS=kafka:9092
KAFKA_GROUP_ID=ai-agent-platform
KAFKA_TOPIC_AGENTS=agent-events
KAFKA_TOPIC_ORCHESTRATION=orchestration-commands
KAFKA_TOPIC_INTELLIGENCE=intelligence-requests

# ==============================================
# API CONFIGURATION
# ==============================================
# Backend
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# CORS
CORS_ORIGINS=http://localhost:3000,http://frontend:3000,https://your-domain.com

# ==============================================
# AI SERVICE PROVIDERS
# ==============================================

OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************
GOOGLE_API_KEY=AIzaSyAsdpDxUdJuWFFzvme6E8cjkzKRqXyXt7w

# Model Configuration
PRIMARY_MODEL=gemini-2.0-flash
VALIDATION_MODEL=claude-3-5-sonnet-20241022
CODE_VERIFICATION_MODEL=gpt-4o
MODEL_TEMPERATURE=0.7
MODEL_MAX_TOKENS=4096
MODEL_TIMEOUT=120
MIN_QUALITY_SCORE=0.85


# ==============================================
# SECURITY & AUTHENTICATION
# ==============================================
# JWT
JWT_SECRET_KEY=your-jwt-secret-key-different-from-main-secret
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
JWT_ALGORITHM=HS256

# ==============================================
# MONITORING & OBSERVABILITY
# ==============================================
# Sentry (Error tracking)
SENTRY_DSN=your-sentry-dsn-here
SENTRY_ENVIRONMENT=development

# Grafana
GRAFANA_ADMIN_PASSWORD=admin
GRAFANA_SECRET_KEY=your-grafana-secret-key
GRAFANA_ROOT_URL=http://localhost:3001

# ElasticSearch (for log aggregation)
ELASTICSEARCH_HOST=elasticsearch:9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=your-elasticsearch-password



